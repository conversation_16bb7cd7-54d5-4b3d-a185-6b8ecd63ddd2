/**
 * <PERSON><PERSON><PERSON><PERSON> - Background Service Worker
 * Handles clipboard monitoring, storage management, and command listeners
 */

// Default settings
const DEFAULT_SETTINGS = {
  historyLimit: 25,
  shortcut: 'Ctrl+Shift+X'
};

// Initialize extension on install
chrome.runtime.onInstalled.addListener(async () => {
  console.log('ClipPie installed');

  // Set default settings if not already set
  const result = await chrome.storage.local.get(['settings', 'clipboardHistory']);
  if (!result.settings) {
    // Add some sample clipboard items for testing
    const sampleItems = [
      {
        id: '1',
        content: 'Hello World! This is a sample clipboard item.',
        timestamp: Date.now() - 300000,
        type: 'text'
      },
      {
        id: '2',
        content: 'https://www.example.com/sample-url',
        timestamp: Date.now() - 200000,
        type: 'text'
      },
      {
        id: '3',
        content: 'This is a longer sample text that demonstrates how the extension handles longer clipboard content. It should be truncated in the tag view but fully visible in the sticky note.',
        timestamp: Date.now() - 100000,
        type: 'text'
      },
      {
        id: '4',
        content: '<EMAIL>',
        timestamp: Date.now() - 50000,
        type: 'text'
      }
    ];

    await chrome.storage.local.set({
      settings: DEFAULT_SETTINGS,
      clipboardHistory: sampleItems
    });
    console.log('Sample clipboard items added for testing');
  }
});

// Listen for keyboard command to toggle overlay
chrome.commands.onCommand.addListener(async (command) => {
  if (command === 'toggle-overlay') {
    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (tab && tab.id) {
        // Since content script is now injected on all pages, just send toggle message
        try {
          await chrome.tabs.sendMessage(tab.id, { action: 'toggleOverlay' });
        } catch (error) {
          // If content script isn't responding, try to inject it manually
          console.log('Content script not responding, attempting manual injection...');
          try {
            await chrome.scripting.executeScript({
              target: { tabId: tab.id },
              files: ['content.js']
            });

            // Wait a bit for injection to complete, then show overlay
            setTimeout(async () => {
              try {
                await chrome.tabs.sendMessage(tab.id, { action: 'showOverlay' });
              } catch (e) {
                console.error('Failed to show overlay after manual injection:', e);
              }
            }, 200);
          } catch (injectionError) {
            console.error('Failed to inject content script:', injectionError);
          }
        }
      }
    } catch (error) {
      console.error('Error handling toggle command:', error);
    }
  }
});

// Add item to clipboard history with FIFO management
async function addToClipboardHistory(text) {
  try {
    const { clipboardHistory = [], settings = DEFAULT_SETTINGS } = await chrome.storage.local.get(['clipboardHistory', 'settings']);

    // Don't add if it's the same as the last item
    if (clipboardHistory.length > 0 && clipboardHistory[0].content === text) {
      return;
    }

    // Create new clipboard item
    const newItem = {
      id: Date.now().toString(),
      content: text,
      timestamp: Date.now(),
      type: 'text' // For future expansion to support images/links
    };

    // Add to beginning of array (most recent first)
    const updatedHistory = [newItem, ...clipboardHistory];

    // Enforce history limit (FIFO - remove oldest items)
    const limitedHistory = updatedHistory.slice(0, settings.historyLimit || DEFAULT_SETTINGS.historyLimit);

    // Save to storage
    await chrome.storage.local.set({ clipboardHistory: limitedHistory });

    console.log('Added to clipboard history:', text.substring(0, 50) + '...');
  } catch (error) {
    console.error('Error adding to clipboard history:', error);
  }
}

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'getClipboardHistory') {
    chrome.storage.local.get(['clipboardHistory']).then(result => {
      sendResponse({ history: result.clipboardHistory || [] });
    });
    return true; // Keep message channel open for async response
  }

  if (message.action === 'clearHistory') {
    chrome.storage.local.set({ clipboardHistory: [] }).then(() => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (message.action === 'updateSettings') {
    chrome.storage.local.set({ settings: message.settings }).then(() => {
      sendResponse({ success: true });
    });
    return true;
  }

  if (message.action === 'addToClipboard') {
    addToClipboardHistory(message.text).then(async () => {
      sendResponse({ success: true });

      // Notify all content scripts to refresh their overlays
      try {
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
          try {
            await chrome.tabs.sendMessage(tab.id, { action: 'refreshOverlay' });
          } catch (e) {
            // Ignore errors for tabs without content script
          }
        }
      } catch (error) {
        console.error('Error notifying content scripts:', error);
      }
    }).catch(error => {
      console.error('Error adding to clipboard:', error);
      sendResponse({ success: false, error: error.message });
    });
    return true;
  }

  if (message.action === 'readClipboard') {
    // This will be handled by content script since service worker can't access clipboard directly
    sendResponse({ success: false, error: 'Clipboard reading must be done from content script' });
    return true;
  }
});
